<template>
  <view class="container">
    <view class="page-header">
      <!-- 数据汇总 -->
      <view class="summary-section">
        <view class="stats-grid">
          <view class="stat-card">
            <view class="stat-value">
              <CountUp :endValue="statisticsData.totalUsers" suffix="人" :duration="1000" customClass="stat-number" />
            </view>
            <text class="stat-label">会员总数</text>
          </view>
          <view class="stat-card">
            <view class="stat-value">
              <CountUp :endValue="statisticsData.newUsersToday" suffix="人" :duration="1000" customClass="stat-number" />
            </view>
            <text class="stat-label">今日新增</text>
          </view>
          <view class="stat-card">
            <view class="stat-value">
              <CountUp :endValue="statisticsData.viewerCount" suffix="人" :duration="1000" customClass="stat-number" />
            </view>
            <text class="stat-label">今日观看</text>
          </view>
        </view>
      </view>

      <!-- 时间筛选 -->
      <view class="time-filter-section">
        <view class="time-filter-title">
          <text>时间筛选</text>
          <text class="time-filter-subtitle">选择查看不同时间段的数据</text>
        </view>
        <TimeFilter v-model="activeTimeFilter" @change="handleTimeFilterChange" />
      </view>
    </view>
    <view class="page-content">
      <!-- 课程统计 -->
      <view class="data-section" v-permission:feature="'view_statistics'">
        <view class="section-header">
          <view class="section-title-wrapper">
            <text class="section-title">课程统计</text>
          </view>
        </view>

        <view class="section-content" v-if="sectionState.courses">
          <view class="stats-grid">
            <view class="stat-card">
              <text class="stat-label">观看次数</text>
              <view class="stat-value">
                <CountUp :endValue="statisticsData.videoStats.viewCount" suffix="次" :duration="1000"
                  customClass="stat-number" />
              </view>
            </view>
            <view class="stat-card">
              <text class="stat-label">完播次数</text>
              <view class="stat-value">
                <CountUp :endValue="statisticsData.videoStats.completeViewCount" suffix="次" :duration="1000"
                  customClass="stat-number" />
              </view>
            </view>
            <view class="stat-card">
              <text class="stat-label">完播率</text>
              <view class="stat-value">
                <CountUp :endValue="statisticsData.videoStats.completeRate" suffix="%" :duration="1000" :decimals="2"
                  customClass="stat-number" />
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 答题统计 -->
      <view class="data-section" v-permission:feature="'view_statistics'">
        <view class="section-header">
          <view class="section-title-wrapper">
            <text class="section-title">答题统计</text>
          </view>
        </view>

        <view class="section-content" v-if="sectionState.quizzes">
          <view class="stats-grid">
            <view class="stat-card">
              <text class="stat-label">答题次数</text>
              <view class="stat-value">
                <CountUp :endValue="statisticsData.quizStats.answerCount" suffix="次" :duration="1000"
                  customClass="stat-number" />
              </view>
            </view>
            <view class="stat-card">
              <text class="stat-label">正确次数</text>
              <view class="stat-value">
                <CountUp :endValue="statisticsData.quizStats.correctAnswerCount" suffix="次" :duration="1000"
                  customClass="stat-number" />
              </view>
            </view>
            <view class="stat-card">
              <text class="stat-label">正确率</text>
              <view class="stat-value">
                <CountUp :endValue="statisticsData.quizStats.correctRate" suffix="%" :duration="1000" :decimals="2"
                  customClass="stat-number" />
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 红包统计 -->
      <view class="data-section" v-permission:feature="'view_statistics'">
        <view class="section-header">
          <view class="section-title-wrapper">
            <text class="section-title">红包统计</text>
          </view>
        </view>

        <view class="section-content" v-if="sectionState.rewards">
          <view class="stats-grid grid-2">
            <view class="stat-card">
              <text class="stat-label">红包数量</text>
              <view class="stat-value">
                <CountUp :endValue="statisticsData.rewardStats.rewardCount" suffix="个" :duration="1000"
                  customClass="stat-number" />
              </view>
            </view>
            <view class="stat-card">
              <text class="stat-label">红包金额</text>
              <view class="stat-value">
                <CountUp :endValue="statisticsData.rewardStats.rewardAmountYuan" suffix="元" :duration="1000"
                  :decimals="2" customClass="stat-number" />
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>

import CountUp from "@/components/CountUp.vue";
import TimeFilter from "@/components/TimeFilter.vue";
import adminAuthService from "@/utils/adminAuthService.js";
import { getStatisticsOverview } from "@/api/statistics.js";
import { getKeyMetrics } from "@/api/dashboard.js";
import permissionMixin from "@/mixins/permission-mixin.js";
import { apiWrapper } from "@/utils/admin-helpers.js";

export default {
  mixins: [permissionMixin],
  components: {
    CountUp,
    TimeFilter,
  },
  data () {
    return {
      // 统计数据 - 使用新的API数据结构，初始值为0避免误导
      statisticsData: {
        // 关键指标
        totalUsers: 0,
        newUsersToday: 0,
        viewerCount: 0,
        completeRate: 0,
        answerUserCount: 0,
        correctRate: 0,
        totalRewardAmount: 0,
        untaggedUserCount: 0,

        // 视频统计
        videoStats: {
          viewCount: 0,
          completeViewCount: 0,
          completeRate: 0,
          avgViewDuration: 0,
        },

        // 答题统计
        quizStats: {
          answerCount: 0,
          correctAnswerCount: 0,
          correctRate: 0,
        },

        // 奖励统计
        rewardStats: {
          rewardCount: 0,
          rewardAmountYuan: 0,
        },
      },
      activeTimeFilter: "today", // 'today', 'yesterday', 'thisWeek', 'thisMonth', 'custom'
      customDateRange: null,
      sectionState: {
        courses: true,
        quizzes: true,
        rewards: true,
      },


    };
  },
  onLoad () {
    // 检查是否是直接访问视频页面的URL
    const currentUrl = window.location.href;
    console.log('首页onLoad - 当前URL:', currentUrl);

    if (currentUrl.includes('/pages/video/index')) {
      // 直接跳转到视频页面，不进行后台认证检查
      console.log('检测到视频页面访问，直接跳转');
      const hash = window.location.hash;
      if (hash && hash.length > 1) {
        const targetUrl = hash.substring(1); // 移除开头的#
        console.log('跳转到视频页面:', targetUrl);
        uni.reLaunch({
          url: targetUrl
        });
        return; // 重要：这里要直接返回，不执行后续的认证检查
      }
    }

    // 使用统一的权限验证
    if (!this.canAccessPage('/pages/index/index')) {
      adminAuthService.redirectToLogin();
      return;
    }

    console.log('当前用户类型:', this.currentUserType);
    this.initDashboardData();
    uni.showTabBar();
  },
  onShow () {
    // 无论登录状态都显示底部导航栏
    uni.showTabBar();
  },
  methods: {
    // 管理后台相关方法
    async initDashboardData () {
      try {
        // 显示加载动画
        uni.showLoading({
          title: "加载数据中...",
        });

        // 调用API获取今日数据 - 现在不会抛出错误
        await this.loadStatisticsData('today');

        uni.hideLoading();
      } catch (error) {
        console.error('初始化数据失败:', error);
        uni.hideLoading();

        // 显示错误提示
        uni.showToast({
          title: "数据加载失败",
          icon: "none",
          duration: 2000
        });
      }
    },

    // 加载统计数据的核心方法
    async loadStatisticsData (timeRange, customStartDate = null, customEndDate = null) {
      try {
        // 构建API参数
        let params = {};

        // 如果提供了自定义日期，直接使用
        if (customStartDate && customEndDate) {
          params = {
            startDate: customStartDate,
            endDate: customEndDate
          };
        } else if (timeRange === 'today') {
          // 默认加载今日数据
          const today = new Date();
          const formatDate = (date) => {
            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            return `${year}-${month}-${day}`;
          };
          params.startDate = formatDate(today);
          params.endDate = formatDate(today);
        }
        // 注意：其他时间范围的处理已经由TimeFilter组件完成，
        // 通过handleTimeFilterChange方法传递正确的startDate和endDate

        let overviewResponse = null;
        let keyMetricsResponse = null;

        try {
          // 调用新的统计API获取数据
          [overviewResponse, keyMetricsResponse] = await Promise.all([
            getStatisticsOverview(params),
            getKeyMetrics(params)
          ]);
        } catch (apiError) {
          console.error('API调用失败:', apiError);
        }

        // 检查API响应
        const overviewSuccess = overviewResponse && overviewResponse.success;
        const keyMetricsSuccess = keyMetricsResponse && keyMetricsResponse.success;

        if (overviewSuccess && keyMetricsSuccess) {
          const overviewData = overviewResponse.data;
          const keyMetricsData = keyMetricsResponse.data;

          // 转换API数据格式为页面需要的格式
          this.statisticsData = {
            // 基础统计 - 使用关键指标数据（新接口只返回3个字段）
            totalUsers: keyMetricsData.totalMembers || 0,
            newUsersToday: keyMetricsData.todayNewMembers || 0,
            viewerCount: keyMetricsData.todayViewers || 0, // 修正：使用 todayViewers 而不是 viewerCount
            completeRate: 0, // 新接口不再返回此字段
            answerUserCount: 0, // 新接口不再返回此字段
            correctRate: 0, // 新接口不再返回此字段
            totalRewardAmount: 0, // 新接口不再返回此字段
            untaggedUserCount: 0, // 新接口不再返回此字段

            // 视频统计 - 使用概览数据
            videoStats: {
              viewCount: overviewData.totalViewCount || 0,
              completeViewCount: overviewData.totalCompleteViewCount || 0,
              completeRate: overviewData.avgCompleteRate || 0,
              avgViewDuration: 0,
            },

            // 答题统计
            quizStats: {
              answerCount: overviewData.totalAnswerCount || 0,
              correctAnswerCount: overviewData.totalCorrectAnswerCount || 0,
              correctRate: overviewData.avgCorrectRate || 0,
            },

            // 奖励统计
            rewardStats: {
              rewardCount: overviewData.totalRewardCount || 0,
              rewardAmountYuan: overviewData.totalRewardAmountYuan || 0,
            },
          };



        } else {
          // API失败时使用模拟数据，不抛出错误
          console.warn('API数据获取失败，使用模拟数据:', {
            overviewSuccess,
            keyMetricsSuccess,
            overviewResponse: overviewResponse ? overviewResponse.msg : 'null',
            keyMetricsResponse: keyMetricsResponse ? keyMetricsResponse.msg : 'null'
          });

          // API失败时不使用模拟数据，保持初始的0值避免误导
          console.warn('API数据获取失败，显示空数据');

          // 显示提示信息
          uni.showToast({
            title: "数据加载失败",
            icon: "none",
            duration: 2000
          });

          // 显示提示信息
          uni.showToast({
            title: "使用演示数据",
            icon: "none",
            duration: 2000
          });
        }

      } catch (error) {
        console.error('加载统计数据失败:', error);

        // 抛出错误，不使用fallback数据
        throw error;
      }
    },

    async selectTimeTab (value) {
      try {
        // 显示加载动画
        uni.showLoading({
          title: "加载中...",
        });

        // 调用API获取对应时间范围的数据 - 现在不会抛出错误
        await this.loadStatisticsData(value);

        uni.hideLoading();
      } catch (error) {
        console.error('切换时间标签失败:', error);
        uni.hideLoading();

        // 显示错误提示
        uni.showToast({
          title: "数据加载失败",
          icon: "none",
          duration: 2000
        });
      }
    },

    async handleTimeFilterChange (timeRange) {
      try {
        // 保存时间筛选信息
        this.activeTimeFilter = timeRange;
        this.customDateRange = {
          startDate: timeRange.startDate,
          endDate: timeRange.endDate
        };

        console.log("时间筛选变化:", timeRange);

        // 显示加载动画
        uni.showLoading({
          title: "加载中...",
        });

        // 调用API获取对应时间范围的数据
        await this.loadStatisticsData(timeRange.type, timeRange.startDate, timeRange.endDate);

        uni.hideLoading();

        // 提示用户
        uni.showToast({
          title: `已加载${timeRange.startDate}至${timeRange.endDate}的数据`,
          icon: "none",
          duration: 2000,
        });
      } catch (error) {
        console.error('时间筛选数据加载失败:', error);
        uni.hideLoading();

        // 显示错误提示
        uni.showToast({
          title: "数据加载失败",
          icon: "none",
          duration: 2000
        });
      }
    },
  },
};
</script>

<style>
/* 页面整体样式优化 */
.container {
  padding: 0 0 40rpx 0;
  background: linear-gradient(180deg, #f0f2f5 0%, #fafafa 100%);

  position: relative;
}

/* 页面背景装饰 */
.container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 300rpx;
  background: linear-gradient(135deg, #186BFF 0%, #186BFF 50%, #186BFF 100%);
  opacity: 0.03;
  z-index: 0;
}





.page-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1;
}

.page-content {
  margin-top: 14.0625rem;
}

/* 管理后台样式 */
.summary-section {
  background-color: #fff;
  padding: 40rpx 30rpx 0;
  /* margin: 20rpx 20rpx 30rpx; */
  /* border-radius: 16rpx; */
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  border-bottom: 0.5px solid #f0f0f0;
}

.section-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
}

.text-center {
  text-align: center;
}

/* 响应式统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160rpx, 1fr));
  gap: 20rpx;
  margin-bottom: 30rpx;
  padding: 0 10rpx;
}

/* 2列网格布局 */
.grid-2 {
  grid-template-columns: repeat(2, 1fr);
  gap: 30rpx;
}

/* 统计卡片样式 */
.stat-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30rpx 20rpx;
  background: #fff;
  border-radius: 10rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 统计内容样式 */
.stat-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

/* 数据区域样式 */
.data-section {
  background: #ffffff;
  /* border-radius: 16rpx;
  margin: 0 20rpx 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05); */
  overflow: hidden;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title-wrapper {
  display: flex;
  align-items: center;
}

.section-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.course-icon {
  background: linear-gradient(135deg, #186BFF, #40a9ff);
  color: #fff;
}

.quiz-icon {
  background: linear-gradient(135deg, #52c41a, #73d13d);
  color: #fff;
}

.reward-icon {
  background: linear-gradient(135deg, #fa541c, #ff7a45);
  color: #fff;
}



.section-more {
  font-size: 26rpx;
  color: #186BFF;
  font-weight: 500;
}

.section-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
}

.section-content {
  padding: 30rpx;
}



/* 统计数值样式优化 */
.stat-value {
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 12rpx;
  line-height: 1.1;
  letter-spacing: -0.5rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  color: #186BFF !important;
}



/* 统计标签样式优化 */
.stat-label {
  font-size: 28rpx;
  /* color: #666; */
  margin-bottom: 8rpx;
  font-weight: 500;
  letter-spacing: 0.5rpx;
  opacity: 0.8;
  transition: all 0.3s;
  text-align: center;
}

/* 悬停时标签效果 */
.stat-card:hover .stat-label {
  color: #333;
  opacity: 1;
}

.stat-compare {
  font-size: 20rpx;
  color: #999;
  margin-bottom: 4rpx;
}

/* 时间筛选样式 */
.time-filter-section {
  background-color: #fff;
  padding: 30rpx 30rpx 0;
  /* margin: 0 20rpx 30rpx; */
  /* border-radius: 16rpx; */
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.time-filter-title {
  font-size: 30rpx;
  font-weight: 600;
  margin-bottom: 20rpx;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.time-filter-subtitle {
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
}





.section-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 统计数值样式 */
.stat-value {
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 12rpx;
  line-height: 1.1;
  letter-spacing: -0.5rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  color: #186BFF !important;
}

.stat-number {
  font-size: 44rpx;
  font-weight: bold;
  font-family: "DIN Alternate", "Arial", sans-serif;
  color: #186BFF !important;
}
</style>